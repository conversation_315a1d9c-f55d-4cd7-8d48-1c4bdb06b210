# 日志轮转功能修改说明

## 修改概述

本次修改将日志轮转功能改为只在debug日志等级时才启动，其他日志等级（info、warn、error）不会执行日志轮转。

## 修改的文件

### 1. src/main.rs

**修改内容：**
- 导入了 `LevelFilter` 类型和 `read_log_level_config` 函数
- 修改了日志轮转监控线程，使其动态响应日志等级变化
- 线程现在每分钟检查一次日志等级配置，只在debug等级时执行日志轮转

**主要变化：**
```rust
// 修改前：无条件启动日志轮转监控
thread::spawn(move || {
    info!("Log rotation monitor started");
    loop {
        thread::sleep(Duration::from_secs(300));
        match check_and_rotate_main_log() { ... }
    }
});

// 修改后：只在debug等级时启动日志轮转
thread::spawn(move || {
    info!("Log rotation monitor thread started");
    let mut rotation_active = false;
    
    loop {
        thread::sleep(Duration::from_secs(60));
        
        match read_log_level_config() {
            Ok(level) => {
                let should_be_active = level == LevelFilter::Debug;
                
                if should_be_active && !rotation_active {
                    info!("Log rotation monitor enabled (debug level detected)");
                    rotation_active = true;
                } else if !should_be_active && rotation_active {
                    info!("Log rotation monitor disabled (debug level no longer active)");
                    rotation_active = false;
                }
                
                // 只在debug等级时执行日志轮转检查
                if rotation_active {
                    match check_and_rotate_main_log() { ... }
                }
            }
            ...
        }
    }
});
```

### 2. src/utils/logger.rs

**修改内容：**
- 修改了 `ensure_log_file` 方法，使其只在debug等级时执行日志轮转检查

**主要变化：**
```rust
// 修改前：无条件执行日志轮转检查
fn ensure_log_file(&self) -> Result<()> {
    let mut writer = self.file_writer.lock().unwrap();
    
    if writer.is_none() {
        // 检查并执行日志轮转
        self.check_and_rotate_log()?;
        ...
    }
    ...
}

// 修改后：只在debug等级时执行日志轮转检查
fn ensure_log_file(&self) -> Result<()> {
    let mut writer = self.file_writer.lock().unwrap();
    
    if writer.is_none() {
        // 只在debug等级时检查并执行日志轮转
        if let Ok(level) = read_log_level_config() {
            if level == LevelFilter::Debug {
                self.check_and_rotate_log()?;
            }
        }
        ...
    }
    ...
}
```

## 功能说明

### 日志轮转行为

1. **Debug等级 (debug)**：
   - 启用日志轮转监控线程
   - 每分钟检查日志等级配置
   - 当日志文件大小超过阈值时执行轮转
   - 在日志写入时也会检查是否需要轮转

2. **其他等级 (info/warn/error)**：
   - 禁用日志轮转监控
   - 不执行日志轮转检查
   - 日志文件会持续增长，不会被轮转

### 动态响应

- 日志轮转监控线程会动态响应日志等级的变化
- 当日志等级从非debug切换到debug时，会启用日志轮转
- 当日志等级从debug切换到其他等级时，会禁用日志轮转
- 相关状态变化会记录到日志中

## 优势

1. **资源节约**：在非debug等级时不执行不必要的日志轮转检查，节约系统资源
2. **灵活性**：支持动态切换，无需重启程序
3. **调试友好**：在debug等级时保持完整的日志轮转功能，便于调试
4. **生产环境优化**：在生产环境（通常使用info等级）中减少磁盘I/O操作

## 注意事项

1. 在非debug等级时，日志文件可能会持续增长，需要手动管理
2. 切换到debug等级后，日志轮转功能会在下一个检查周期（最多1分钟）内生效
3. 现有的手动日志轮转功能（如WebUI中的强制轮转）不受此修改影响
