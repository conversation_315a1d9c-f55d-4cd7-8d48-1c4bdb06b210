#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use std::path::Path;
    use tempfile::tempdir;

    #[test]
    fn test_log_rotation_only_in_debug() {
        // 创建临时目录用于测试
        let temp_dir = tempdir().unwrap();
        let temp_log_path = temp_dir.path().join("test_log.log");
        let temp_level_path = temp_dir.path().join("log_level");

        // 测试非debug等级时不执行日志轮转
        fs::write(&temp_level_path, "info").unwrap();
        
        // 模拟大日志文件
        let large_content = "x".repeat(15 * 1024 * 1024); // 15MB
        fs::write(&temp_log_path, large_content).unwrap();
        
        // 在非debug等级时，日志轮转不应该被触发
        // 这里我们需要模拟实际的日志轮转检查逻辑
        
        // 测试debug等级时执行日志轮转
        fs::write(&temp_level_path, "debug").unwrap();
        
        // 在debug等级时，日志轮转应该被触发
        // 这里我们需要模拟实际的日志轮转检查逻辑
        
        // 清理
        temp_dir.close().unwrap();
    }

    #[test]
    fn test_read_log_level_config() {
        let temp_dir = tempdir().unwrap();
        let temp_level_path = temp_dir.path().join("log_level");

        // 测试debug等级
        fs::write(&temp_level_path, "debug").unwrap();
        // 这里需要修改read_log_level_config函数来接受自定义路径
        
        // 测试info等级
        fs::write(&temp_level_path, "info").unwrap();
        
        // 测试warn等级
        fs::write(&temp_level_path, "warn").unwrap();
        
        // 测试error等级
        fs::write(&temp_level_path, "error").unwrap();
        
        // 测试无效等级
        fs::write(&temp_level_path, "invalid").unwrap();
        
        temp_dir.close().unwrap();
    }
}
